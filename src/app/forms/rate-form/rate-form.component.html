<form [formGroup]="form" class="flex-row no-overflow">
   <div class="flex-column" style="margin-right: 20px">
      <mat-form-field>
         <mat-label>Име</mat-label>
         <input autocomplete="off" formControlName="name" matInput type="text">
      </mat-form-field>

      <app-reservation-source-input [control]="reservationSources" [multiple]="true"/>
      
      <div class="bundle-entries-section">
         <div class="apart-row">
            <h3>Цени на пакети</h3>
            <button (click)="addBundleEntry()" mat-icon-button type="button">
               <mat-icon>add</mat-icon>
            </button>
         </div>
         
         <div formArrayName="entries">
            @for (entryControl of entries.controls; track entryControl) {
               <div [formGroupName]="$index" class="bundle-entry">
                  <mat-form-field>
                     <mat-label>Пакет</mat-label>
                     <mat-select formControlName="bundleId">
                        @for (bundle of bundles; track bundle.id) {
                           <mat-option [value]="bundle.id">{{bundle.name}}</mat-option>
                        }
                     </mat-select>
                  </mat-form-field>
                  
                  <div formGroupName="bundleRate">
                     <div formGroupName="price" class="price-input">
                        <mat-form-field>
                           <mat-label>Цена</mat-label>
                           <input autocomplete="off" formControlName="amount" matInput type="number">
                        </mat-form-field>
                        <mat-form-field>
                           <mat-label>Валута</mat-label>
                           <mat-select formControlName="currency">
                              <mat-option value="BGN">BGN</mat-option>
                              <mat-option value="EUR">EUR</mat-option>
                              <mat-option value="USD">USD</mat-option>
                           </mat-select>
                        </mat-form-field>
                     </div>
                  </div>
                  
                  <button (click)="entries.removeAt($index)" mat-icon-button color="warn" type="button">
                     <mat-icon>remove_circle</mat-icon>
                  </button>
               </div>
            }
         </div>
      </div>
   </div>
   
   <div>
      <div class="flex-column add-range">
         <mat-form-field style="height: 0; width: 0; visibility:hidden;">
            <mat-date-range-input [rangePicker]="picker">
               <input [formControl]="start" matStartDate placeholder="Началo">
               <input [formControl]="end" matEndDate placeholder="Край">
            </mat-date-range-input>
            <mat-date-range-picker #picker></mat-date-range-picker>
         </mat-form-field>
         <div>
            <button (click)="picker.open()" color="primary" mat-raised-button type="button">
               <mat-icon>add</mat-icon>
               Добавяне на период
            </button>
         </div>
      </div>
      
      <div class="ranges" formArrayName="activeRanges">
         @for (_ of activeRanges.controls; track _) {
            <div [formGroupName]="$index" class="range four-column-grid">
               <div class="colspan-3">
                  {{getDateRange($index) | dateRanges}}
               </div>
               <div class="remove-range colspan-1">
                  <button (click)="activeRanges.removeAt($index)" mat-icon-button color="warn" type="button">
                     <mat-icon>remove_circle</mat-icon>
                  </button>
               </div>
               <div class="colspan-2"></div>
               <mat-form-field class="colspan-4">
                  <mat-label>Активни дни</mat-label>
                  <mat-select formControlName="weekDays" multiple>
                     @for (day of days | keyvalue: originalOrder; track day) {
                        <mat-option [value]="day.key">{{day.value}}</mat-option>
                     }
                  </mat-select>
               </mat-form-field>
            </div>
         }
      </div>
   </div>
</form>
